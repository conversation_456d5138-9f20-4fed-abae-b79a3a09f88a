-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- <PERSON><PERSON><PERSON> il: Mag 27, 2025 alle 08:36
-- Versione del server: 9.1.0
-- Versione PHP: 8.3.14

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `pbm`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON><PERSON> della tabella `cache`
--

DROP TABLE IF EXISTS `cache`;
CREATE TABLE IF NOT EXISTS `cache` (
  `key` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struttura della tabella `cache_locks`
--

DROP TABLE IF EXISTS `cache_locks`;
CREATE TABLE IF NOT EXISTS `cache_locks` (
  `key` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struttura della tabella `cash_movements`
--

DROP TABLE IF EXISTS `cash_movements`;
CREATE TABLE IF NOT EXISTS `cash_movements` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `worker_id` bigint UNSIGNED NOT NULL,
  `work_id` bigint UNSIGNED DEFAULT NULL,
  `tipo_movimento` enum('entrata','uscita') COLLATE utf8mb4_unicode_ci NOT NULL,
  `importo` decimal(10,2) NOT NULL,
  `motivo` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `metodo_pagamento` enum('contanti','dkv','carta') COLLATE utf8mb4_unicode_ci NOT NULL,
  `credit_card_id` bigint UNSIGNED DEFAULT NULL,
  `data_movimento` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cash_movements_worker_id_foreign` (`worker_id`),
  KEY `cash_movements_work_id_foreign` (`work_id`),
  KEY `cash_movements_credit_card_id_foreign` (`credit_card_id`)
) ENGINE=MyISAM AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `cash_movements`
--

INSERT INTO `cash_movements` (`id`, `worker_id`, `work_id`, `tipo_movimento`, `importo`, `motivo`, `metodo_pagamento`, `credit_card_id`, `data_movimento`, `created_at`, `updated_at`) VALUES
(1, 6, 5, 'entrata', 100.00, 'Incasso per il lavoro #5', 'contanti', NULL, '2025-05-17', '2025-05-17 08:24:14', '2025-05-17 08:24:14'),
(2, 6, NULL, 'uscita', 50.00, 'benzina', 'contanti', NULL, '2025-05-17', '2025-05-17 08:28:22', '2025-05-17 08:28:22'),
(3, 6, NULL, 'entrata', 50.00, 'Ricarica fondo cassa: ricarica', 'contanti', NULL, '2025-05-17', '2025-05-17 08:52:39', '2025-05-17 08:52:39'),
(4, 6, NULL, 'uscita', 10.00, 'panino', 'contanti', NULL, '2025-05-17', '2025-05-17 08:54:12', '2025-05-17 08:54:12'),
(5, 6, NULL, 'uscita', 11.00, 'ffff', 'contanti', NULL, '2025-05-17', '2025-05-17 09:04:58', '2025-05-17 09:04:58'),
(6, 6, NULL, 'uscita', 10.00, 'iuiui', 'carta', 3, '2025-05-17', '2025-05-17 09:50:36', '2025-05-17 09:50:36'),
(7, 6, NULL, 'uscita', 20.00, 'benzina', 'contanti', NULL, '2025-05-19', '2025-05-19 13:33:21', '2025-05-19 13:33:21'),
(8, 6, NULL, 'uscita', 10.00, 'panino', 'carta', 3, '2025-05-19', '2025-05-19 13:33:47', '2025-05-19 13:33:47'),
(9, 6, NULL, 'uscita', 10.00, 'benzina', 'carta', 3, '2025-05-21', '2025-05-21 11:04:41', '2025-05-21 11:04:41'),
(10, 6, NULL, 'uscita', 1.00, 'caffè', 'contanti', NULL, '2025-05-21', '2025-05-21 11:04:59', '2025-05-21 11:04:59'),
(11, 6, NULL, 'uscita', 20.00, 'Rifornimento benzina', 'carta', 3, '2025-05-21', '2025-05-21 13:05:46', '2025-05-21 13:05:46'),
(12, 6, 13, 'entrata', 50.00, 'Incasso per il lavoro #13', 'contanti', NULL, '2025-05-21', '2025-05-21 13:09:02', '2025-05-21 13:09:02'),
(13, 6, NULL, 'entrata', 20.00, 'Ricarica fondo cassa: finito soldi', 'contanti', NULL, '2025-05-21', '2025-05-21 13:20:56', '2025-05-21 13:20:56');

-- --------------------------------------------------------

--
-- Struttura della tabella `credit_cards`
--

DROP TABLE IF EXISTS `credit_cards`;
CREATE TABLE IF NOT EXISTS `credit_cards` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `numero_carta` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `scadenza_carta` date NOT NULL,
  `fondo_carta` decimal(10,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `credit_cards`
--

INSERT INTO `credit_cards` (`id`, `numero_carta`, `scadenza_carta`, `fondo_carta`, `created_at`, `updated_at`) VALUES
(3, '26262626', '2025-06-17', 340.00, '2025-03-28 10:17:31', '2025-05-21 13:05:46'),
(5, '****************', '2027-10-23', 100.00, '2025-05-21 13:41:18', '2025-05-21 13:41:18');

-- --------------------------------------------------------

--
-- Struttura della tabella `credit_card_recharges`
--

DROP TABLE IF EXISTS `credit_card_recharges`;
CREATE TABLE IF NOT EXISTS `credit_card_recharges` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `credit_card_id` bigint UNSIGNED NOT NULL,
  `importo` decimal(10,2) NOT NULL,
  `data_ricarica` datetime NOT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `credit_card_recharges_credit_card_id_foreign` (`credit_card_id`),
  KEY `credit_card_recharges_user_id_foreign` (`user_id`)
) ENGINE=MyISAM AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `credit_card_recharges`
--

INSERT INTO `credit_card_recharges` (`id`, `credit_card_id`, `importo`, `data_ricarica`, `note`, `created_at`, `updated_at`, `user_id`) VALUES
(6, 3, -10.00, '2025-05-17 11:50:36', 'Spesa: iuiui', '2025-05-17 09:50:36', '2025-05-17 09:50:36', 8),
(3, 3, 50.00, '2025-03-28 00:00:00', 'eee', '2025-03-28 11:33:51', '2025-03-28 11:33:51', 1),
(4, 3, 90.00, '2025-03-28 12:41:00', 'Test', '2025-03-28 11:41:56', '2025-03-28 11:41:56', 1),
(7, 3, 50.00, '2025-05-17 12:45:00', NULL, '2025-05-17 10:45:43', '2025-05-17 10:45:43', 1),
(8, 3, -10.00, '2025-05-19 15:33:47', 'Spesa: panino', '2025-05-19 13:33:47', '2025-05-19 13:33:47', 8),
(9, 3, -10.00, '2025-05-21 13:04:41', 'Spesa: benzina', '2025-05-21 11:04:41', '2025-05-21 11:04:41', 8),
(10, 3, -20.00, '2025-05-21 15:05:46', 'Spesa: Rifornimento benzina', '2025-05-21 13:05:46', '2025-05-21 13:05:46', 8);

-- --------------------------------------------------------

--
-- Struttura della tabella `credit_card_worker`
--

DROP TABLE IF EXISTS `credit_card_worker`;
CREATE TABLE IF NOT EXISTS `credit_card_worker` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `credit_card_id` bigint UNSIGNED NOT NULL,
  `worker_id` bigint UNSIGNED NOT NULL,
  `data_assegnazione` date NOT NULL,
  `data_restituzione` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `credit_card_worker_credit_card_id_foreign` (`credit_card_id`),
  KEY `credit_card_worker_worker_id_foreign` (`worker_id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `credit_card_worker`
--

INSERT INTO `credit_card_worker` (`id`, `credit_card_id`, `worker_id`, `data_assegnazione`, `data_restituzione`, `created_at`, `updated_at`) VALUES
(1, 2, 4, '2025-03-28', NULL, '2025-03-28 10:02:37', '2025-03-28 10:02:58'),
(2, 3, 6, '2025-03-29', NULL, '2025-03-28 14:09:58', '2025-05-17 08:11:33'),
(3, 4, 5, '2025-05-17', NULL, '2025-05-17 08:06:08', '2025-05-17 08:06:08'),
(4, 5, 7, '2025-05-21', NULL, '2025-05-21 13:41:32', '2025-05-21 13:41:32');

-- --------------------------------------------------------

--
-- Struttura della tabella `customers`
--

DROP TABLE IF EXISTS `customers`;
CREATE TABLE IF NOT EXISTS `customers` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `customer_type` enum('fisica','giuridica') COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `codice_fiscale` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ragione_sociale` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `partita_iva` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude_customer` decimal(10,7) DEFAULT NULL,
  `longitude_customer` decimal(10,7) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `customers`
--

INSERT INTO `customers` (`id`, `customer_type`, `full_name`, `codice_fiscale`, `ragione_sociale`, `partita_iva`, `address`, `phone`, `email`, `latitude_customer`, `longitude_customer`, `created_at`, `updated_at`) VALUES
(1, 'fisica', 'Nicolò Pira', '942383943ufoi', NULL, NULL, 'Via Appia Antica, Roma, RM, Italia', '323928392', '<EMAIL>', 41.8183952, 12.5584069, '2025-03-18 13:22:53', '2025-03-18 15:17:37'),
(2, 'giuridica', NULL, NULL, 'Asdrubalini srls', 'it9438493843948', 'Via Salaria, Roma, RM, Italia', '2109012921', '<EMAIL>', 41.9873505, 12.5114952, '2025-03-18 15:18:16', '2025-03-18 15:18:16'),
(3, 'fisica', 'Giovanni Castaldo', 'jfgsifjsodfj', NULL, NULL, 'Via Giotto, 13, Caserta, CE, Italia', '473647436', '<EMAIL>', 41.0798348, 14.3343470, '2025-03-18 15:18:47', '2025-03-18 15:18:47'),
(4, 'fisica', 'Marina Tordiglionewe', 'mrndjdaoisjd', NULL, NULL, 'Corso Luigi Cadorna, Campanarello, AV, Italia', '3256165165', '<EMAIL>', 41.0473890, 14.9175705, '2025-03-28 14:40:41', '2025-03-28 14:40:50');

-- --------------------------------------------------------

--
-- Struttura della tabella `deposits`
--

DROP TABLE IF EXISTS `deposits`;
CREATE TABLE IF NOT EXISTS `deposits` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude` decimal(10,7) DEFAULT NULL,
  `longitude` decimal(10,7) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `deposits`
--

INSERT INTO `deposits` (`id`, `name`, `address`, `latitude`, `longitude`, `created_at`, `updated_at`) VALUES
(1, 'Deposito 1', 'Via Conca d\'Oro, Roma, RM, Italia', 41.9405202, 12.5243597, '2025-03-18 13:24:06', '2025-03-18 13:24:06'),
(2, 'Deposito 2', 'Via Napoli, Pozzuoli, NA, Italia', 40.8187237, 14.1460694, '2025-03-18 14:29:08', '2025-03-18 14:29:08'),
(3, 'Isola Ecologica Caserta', 'Via Cappuccini, Caserta, CE, Italia', 41.0862173, 14.3410947, '2025-03-28 14:40:07', '2025-03-28 14:40:07'),
(4, 'Inerti Lazio srl', 'Via Casal Bianco, 269, Guidonia Montecelio, RM, Italia', 41.9545425, 12.6599241, '2025-05-21 13:25:59', '2025-05-21 13:25:59');

-- --------------------------------------------------------

--
-- Struttura della tabella `deposit_material`
--

DROP TABLE IF EXISTS `deposit_material`;
CREATE TABLE IF NOT EXISTS `deposit_material` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `deposit_id` bigint UNSIGNED NOT NULL,
  `material_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `deposit_material_deposit_id_foreign` (`deposit_id`),
  KEY `deposit_material_material_id_foreign` (`material_id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `deposit_material`
--

INSERT INTO `deposit_material` (`id`, `deposit_id`, `material_id`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, NULL),
(2, 2, 2, NULL, NULL),
(3, 3, 1, NULL, NULL),
(4, 3, 2, NULL, NULL),
(5, 4, 3, NULL, NULL);

-- --------------------------------------------------------

--
-- Struttura della tabella `failed_jobs`
--

DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE IF NOT EXISTS `failed_jobs` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struttura della tabella `jobs`
--

DROP TABLE IF EXISTS `jobs`;
CREATE TABLE IF NOT EXISTS `jobs` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `queue` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint UNSIGNED NOT NULL,
  `reserved_at` int UNSIGNED DEFAULT NULL,
  `available_at` int UNSIGNED NOT NULL,
  `created_at` int UNSIGNED NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struttura della tabella `job_batches`
--

DROP TABLE IF EXISTS `job_batches`;
CREATE TABLE IF NOT EXISTS `job_batches` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struttura della tabella `materials`
--

DROP TABLE IF EXISTS `materials`;
CREATE TABLE IF NOT EXISTS `materials` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `eer_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `materials`
--

INSERT INTO `materials` (`id`, `name`, `eer_code`, `created_at`, `updated_at`) VALUES
(1, 'legno', '137/07', '2025-03-18 13:23:52', '2025-03-18 13:23:52'),
(2, 'ferro', '117/54', '2025-03-18 14:28:48', '2025-03-18 14:28:48'),
(3, 'calcinaccio', '11708', '2025-03-28 14:39:45', '2025-03-28 14:39:45');

-- --------------------------------------------------------

--
-- Struttura della tabella `migrations`
--

DROP TABLE IF EXISTS `migrations`;
CREATE TABLE IF NOT EXISTS `migrations` (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `migration` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2025_02_24_114220_add_fields_to_users_table', 1),
(5, '2025_02_24_133313_create_materials_table', 1),
(6, '2025_02_24_135101_create_deposits_table', 1),
(7, '2025_02_24_135141_create_deposit_material_table', 1),
(8, '2025_03_18_135018_create_workers_table', 2),
(9, '2025_03_18_140136_add_phone_to_users_table', 3),
(10, '2025_03_18_140146_add_worker_email_to_workers_table', 4),
(11, '2025_02_24_143432_add_latitude_longitude_to_deposits_table', 5),
(12, '2025_03_10_002448_add_eer_code_to_materials_table', 5),
(13, '2025_03_10_080127_create_customers_table', 5),
(14, '2025_03_10_103027_create_warehouses_table', 6),
(15, '2025_03_10_120913_create_works_table', 6),
(16, '2025_03_18_141655_create_work_worker_table', 7),
(17, '2025_03_18_152217_add_departure_fields_to_works_table', 8),
(18, '2025_03_18_152230_add_departure_fields_to_works_table', 8),
(19, '2025_03_18_161402_add_status_to_works_table', 9),
(20, '2025_03_28_093121_add_execution_cost_payment_fields_to_works_table', 10),
(21, '2025_03_28_094547_create_vehicles_table', 11),
(22, '2025_03_28_103132_create_vehicle_worker_table', 12),
(23, '2025_03_28_104640_create_credit_cards_table', 13),
(24, '2025_03_28_105558_create_credit_card_worker_table', 14),
(25, '2025_03_28_110742_create_credit_card_recharges_table', 15),
(26, '2025_03_28_112614_create_vehicle_assignment_logs_table', 16),
(27, '2025_03_28_112757_add_data_restituzione_to_vehicle_worker_table', 17),
(28, '2025_03_28_113421_recreate_vehicle_worker_table', 18),
(29, '2025_03_28_114057_modify_vehicle_worker_dates_to_datetime', 19),
(30, '2025_03_28_114600_modify_vehicle_assignment_logs_dates_to_datetime', 20),
(31, '2025_03_28_122854_add_autore_to_credit_card_recharges_table', 21),
(32, '2025_03_28_123632_modify_data_ricarica_to_datetime', 22),
(33, '2025_05_20_create_cash_movements_table', 23),
(34, '2025_05_17_104812_add_fondo_cassa_to_workers_table', 24),
(35, '2025_05_17_130503_create_ricevute_table', 25);

-- --------------------------------------------------------

--
-- Struttura della tabella `password_reset_tokens`
--

DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE IF NOT EXISTS `password_reset_tokens` (
  `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struttura della tabella `ricevute`
--

DROP TABLE IF EXISTS `ricevute`;
CREATE TABLE IF NOT EXISTS `ricevute` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `work_id` bigint UNSIGNED NOT NULL,
  `numero_ricevuta` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `fattura` tinyint(1) NOT NULL DEFAULT '0',
  `riserva_controlli` tinyint(1) NOT NULL DEFAULT '0',
  `nome_ricevente` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `firma_base64` text COLLATE utf8mb4_unicode_ci,
  `pagamento_effettuato` tinyint(1) NOT NULL DEFAULT '0',
  `somma_pagamento` decimal(10,2) DEFAULT NULL,
  `foto_bolla` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ricevute_numero_ricevuta_unique` (`numero_ricevuta`),
  KEY `ricevute_work_id_foreign` (`work_id`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `ricevute`
--

INSERT INTO `ricevute` (`id`, `work_id`, `numero_ricevuta`, `fattura`, `riserva_controlli`, `nome_ricevente`, `firma_base64`, `pagamento_effettuato`, `somma_pagamento`, `foto_bolla`, `created_at`, `updated_at`) VALUES
(1, 8, 'TEP-0001', 0, 0, 'adasdsda', NULL, 0, NULL, 'bolle/OQFmogRzUZsNWgEbxZ91lTjnoB8zVGwbP4SjglY8.jpg', '2025-05-17 11:15:12', '2025-05-17 11:15:12'),
(2, 5, 'TEP-0002', 0, 0, 'Tizio Caio', 'data:image/png;base64,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', 1, 100.00, 'bolle/H3UmJlqUKqrfaqPd6KGhWIDbVZlMJcFFGPBAX8YC.jpg', '2025-05-17 11:42:56', '2025-05-17 11:42:56'),
(3, 9, 'TEP-0003', 1, 1, 'Paolo Noli', 'text-signature', 1, 200.00, 'bolle/f0WmZIdEVheXmkN67ZJSXwHKwPARExT8NCNMQUmW.jpg', '2025-05-17 12:08:22', '2025-05-17 12:08:22'),
(4, 9, 'TEP-0004', 1, 1, 'MArina torgihglio', 'data:image/png;base64,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', 1, 200.00, 'bolle/rMAO13B0L8gQ8944t5o55sUFW2Ek2cCeg3j72IQ8.jpg', '2025-05-17 12:12:10', '2025-05-17 12:12:10');
INSERT INTO `ricevute` (`id`, `work_id`, `numero_ricevuta`, `fattura`, `riserva_controlli`, `nome_ricevente`, `firma_base64`, `pagamento_effettuato`, `somma_pagamento`, `foto_bolla`, `created_at`, `updated_at`) VALUES
(5, 11, 'TEP-0005', 1, 1, 'Tizio Caio', 'data:image/png;base64,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', 1, 200.00, 'bolle/i6VtWPh0G17UInob6p4zaTuqZtkvq9CwWZhADMZe.jpg', '2025-05-19 13:33:01', '2025-05-19 13:33:01');
INSERT INTO `ricevute` (`id`, `work_id`, `numero_ricevuta`, `fattura`, `riserva_controlli`, `nome_ricevente`, `firma_base64`, `pagamento_effettuato`, `somma_pagamento`, `foto_bolla`, `created_at`, `updated_at`) VALUES
(6, 12, 'TEP-0006', 0, 1, 'Paolo Noli', 'data:image/png;base64,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', 1, 200.00, 'bolle/kKrFe14ZIEhYreoGv5iIduBXXdyZqMWrAl1hrhEd.png', '2025-05-21 11:04:13', '2025-05-21 11:04:13');
INSERT INTO `ricevute` (`id`, `work_id`, `numero_ricevuta`, `fattura`, `riserva_controlli`, `nome_ricevente`, `firma_base64`, `pagamento_effettuato`, `somma_pagamento`, `foto_bolla`, `created_at`, `updated_at`) VALUES
(7, 13, 'TEP-0007', 0, 1, 'Mario Rossi', 'data:image/png;base64,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', 0, NULL, 'bolle/H3AleooMbLZUq8pCTYpbgHYLCY5JV3o8uT47IEZt.png', '2025-05-21 13:11:01', '2025-05-21 13:11:01');

-- --------------------------------------------------------

--
-- Struttura della tabella `sessions`
--

DROP TABLE IF EXISTS `sessions`;
CREATE TABLE IF NOT EXISTS `sessions` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('kklKwoSVuowfbTjk1CMB7xq6V0s67RKkBOWtvWar', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiQVQzSmRtTURDWWJWOFVUemFSZUVKRDBJQ3BnNTdIemppc1RHSWZsRSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzE6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9tYXRlcmlhbHMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO30=', 1748273619);

-- --------------------------------------------------------

--
-- Struttura della tabella `users`
--

DROP TABLE IF EXISTS `users`;
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `first_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('sviluppatore','amministratore','dipendente') COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=MyISAM AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `users`
--

INSERT INTO `users` (`id`, `first_name`, `last_name`, `email`, `role`, `phone`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'Jojo', 'Castaldo', '<EMAIL>', 'sviluppatore', NULL, NULL, '$2y$12$eLbVzL52Ns8WrfmyFYqymeFkUPLtNal6GJZTboJCD2zxMdz0pnDLq', NULL, '2025-03-18 12:58:14', '2025-03-18 12:58:14'),
(9, 'Alex', 'Blaj', '<EMAIL>', 'dipendente', NULL, NULL, '$2y$12$wVA.JJQU0N0g/rwMY805x.Ts9kTXMCxho/dlVCuVQEwubF8fMYh9S', NULL, '2025-05-21 13:39:33', '2025-05-21 13:39:33'),
(6, 'Porco', 'Minchia', '<EMAIL>', 'dipendente', NULL, NULL, '$2y$12$PcDM4xieq5mJMgJrXypiOevCIFLlgSnPAD4CksBA82S9tfHi/09ny', NULL, '2025-05-17 07:04:42', '2025-05-17 07:04:42'),
(7, 'Erminio', 'Ottone', '<EMAIL>', 'dipendente', NULL, NULL, '$2y$12$4UggQ5wXCppv7.XeBNFw.ur3ZBgKjkgOZmXaEIVpAj2Ld8E.ASjie', NULL, '2025-05-17 07:19:36', '2025-05-17 07:19:36'),
(8, 'Ciccio', 'PAsticcio', '<EMAIL>', 'dipendente', NULL, NULL, '$2y$12$LstaBCORYTpp0Ma0YlS0MebCKr4thel1aLEhUTyfdE7EOAG/U2vxi', NULL, '2025-05-17 08:09:42', '2025-05-17 08:09:42');

-- --------------------------------------------------------

--
-- Struttura della tabella `vehicles`
--

DROP TABLE IF EXISTS `vehicles`;
CREATE TABLE IF NOT EXISTS `vehicles` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `nome` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `targa` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `scadenza_assicurazione` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `vehicles_targa_unique` (`targa`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `vehicles`
--

INSERT INTO `vehicles` (`id`, `nome`, `targa`, `scadenza_assicurazione`, `created_at`, `updated_at`) VALUES
(7, 'Furgoncino', 'bb123TT', '2025-10-08', '2025-05-21 13:42:10', '2025-05-21 13:42:10'),
(6, 'Camion nuovo', 'AS2239SA', '2025-03-13', '2025-03-28 14:42:44', '2025-03-28 14:42:44');

-- --------------------------------------------------------

--
-- Struttura della tabella `vehicle_assignment_logs`
--

DROP TABLE IF EXISTS `vehicle_assignment_logs`;
CREATE TABLE IF NOT EXISTS `vehicle_assignment_logs` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `vehicle_id` bigint UNSIGNED NOT NULL,
  `worker_id` bigint UNSIGNED NOT NULL,
  `data_assegnazione` datetime NOT NULL,
  `data_restituzione` datetime DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `operazione` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vehicle_assignment_logs_vehicle_id_foreign` (`vehicle_id`),
  KEY `vehicle_assignment_logs_worker_id_foreign` (`worker_id`)
) ENGINE=MyISAM AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `vehicle_assignment_logs`
--

INSERT INTO `vehicle_assignment_logs` (`id`, `vehicle_id`, `worker_id`, `data_assegnazione`, `data_restituzione`, `note`, `operazione`, `created_at`, `updated_at`) VALUES
(9, 5, 4, '2025-03-28 12:14:00', NULL, NULL, 'assegnazione', '2025-03-28 11:14:31', '2025-03-28 11:14:31'),
(11, 5, 3, '2025-03-28 15:42:00', NULL, NULL, 'assegnazione', '2025-03-28 14:43:05', '2025-03-28 14:43:05'),
(10, 5, 4, '2025-03-28 12:14:00', '2025-03-28 12:15:23', NULL, 'restituzione', '2025-03-28 11:15:23', '2025-03-28 11:15:23'),
(8, 5, 3, '2025-03-28 11:56:00', '2025-03-28 11:56:20', NULL, 'restituzione', '2025-03-28 10:56:20', '2025-03-28 10:56:20'),
(12, 6, 6, '2025-05-17 12:43:00', NULL, NULL, 'assegnazione', '2025-05-17 10:43:12', '2025-05-17 10:43:12'),
(13, 7, 7, '2025-05-21 15:42:00', NULL, NULL, 'assegnazione', '2025-05-21 13:42:34', '2025-05-21 13:42:34');

-- --------------------------------------------------------

--
-- Struttura della tabella `vehicle_worker`
--

DROP TABLE IF EXISTS `vehicle_worker`;
CREATE TABLE IF NOT EXISTS `vehicle_worker` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `vehicle_id` bigint UNSIGNED NOT NULL,
  `worker_id` bigint UNSIGNED NOT NULL,
  `data_assegnazione` datetime NOT NULL,
  `data_restituzione` datetime DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vehicle_worker_vehicle_id_foreign` (`vehicle_id`),
  KEY `vehicle_worker_worker_id_foreign` (`worker_id`)
) ENGINE=MyISAM AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `vehicle_worker`
--

INSERT INTO `vehicle_worker` (`id`, `vehicle_id`, `worker_id`, `data_assegnazione`, `data_restituzione`, `note`, `created_at`, `updated_at`) VALUES
(1, 2, 3, '2025-03-28 00:00:00', NULL, NULL, '2025-03-28 10:35:17', '2025-03-28 10:35:17'),
(2, 2, 4, '2025-03-29 00:00:00', NULL, NULL, '2025-03-28 10:35:47', '2025-03-28 10:35:47'),
(3, 3, 3, '2025-03-28 11:42:00', NULL, 'bla bla', '2025-03-28 10:42:57', '2025-03-28 10:42:57'),
(4, 3, 3, '2025-03-28 11:44:00', NULL, NULL, '2025-03-28 10:44:35', '2025-03-28 10:44:35'),
(5, 2, 4, '2025-03-28 11:46:00', NULL, NULL, '2025-03-28 10:46:46', '2025-03-28 10:46:46'),
(6, 5, 3, '2025-03-28 11:56:00', '2025-03-28 11:56:20', NULL, '2025-03-28 10:56:04', '2025-03-28 10:56:04'),
(7, 5, 4, '2025-03-28 12:14:00', '2025-03-28 12:15:23', NULL, '2025-03-28 11:14:31', '2025-03-28 11:14:31'),
(8, 5, 3, '2025-03-28 15:42:00', NULL, NULL, '2025-03-28 14:43:05', '2025-03-28 14:43:05'),
(9, 6, 6, '2025-05-17 12:43:00', NULL, NULL, '2025-05-17 10:43:12', '2025-05-17 10:43:12'),
(10, 7, 7, '2025-05-21 15:42:00', NULL, NULL, '2025-05-21 13:42:34', '2025-05-21 13:42:34');

-- --------------------------------------------------------

--
-- Struttura della tabella `warehouses`
--

DROP TABLE IF EXISTS `warehouses`;
CREATE TABLE IF NOT EXISTS `warehouses` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `nome_sede` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `indirizzo` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude_warehouse` decimal(10,7) DEFAULT NULL,
  `longitude_warehouse` decimal(10,7) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `warehouses`
--

INSERT INTO `warehouses` (`id`, `nome_sede`, `indirizzo`, `latitude_warehouse`, `longitude_warehouse`, `created_at`, `updated_at`) VALUES
(1, 'Cantiere 1', 'Via del Corso, Roma, RM, Italia', 41.9032103, 12.4795059, '2025-03-18 13:24:26', '2025-03-18 13:24:26');

-- --------------------------------------------------------

--
-- Struttura della tabella `workers`
--

DROP TABLE IF EXISTS `workers`;
CREATE TABLE IF NOT EXISTS `workers` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `id_worker` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_worker` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `cognome_worker` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `license_worker` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `worker_email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `fondo_cassa` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workers_id_worker_unique` (`id_worker`),
  UNIQUE KEY `workers_worker_email_unique` (`worker_email`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `workers`
--

INSERT INTO `workers` (`id`, `id_worker`, `name_worker`, `cognome_worker`, `license_worker`, `worker_email`, `fondo_cassa`, `created_at`, `updated_at`) VALUES
(7, '434', 'Alex', 'Blaj', '2387328', '<EMAIL>', 0.00, '2025-05-21 13:39:32', '2025-05-21 13:39:32'),
(6, '77', 'Ciccio', 'PAsticcio', '948459348', '<EMAIL>', 78.00, '2025-05-17 08:09:41', '2025-05-21 13:20:56');

-- --------------------------------------------------------

--
-- Struttura della tabella `works`
--

DROP TABLE IF EXISTS `works`;
CREATE TABLE IF NOT EXISTS `works` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `tipo_lavoro` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_id` bigint UNSIGNED NOT NULL,
  `status_lavoro` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'In Sospeso',
  `data_esecuzione` date DEFAULT NULL,
  `costo_lavoro` decimal(10,2) DEFAULT NULL,
  `modalita_pagamento` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `nome_partenza` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `indirizzo_partenza` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `latitude_partenza` decimal(10,7) DEFAULT NULL,
  `longitude_partenza` decimal(10,7) DEFAULT NULL,
  `materiale` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `codice_eer` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `nome_destinazione` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `indirizzo_destinazione` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude_destinazione` decimal(10,7) DEFAULT NULL,
  `longitude_destinazione` decimal(10,7) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `works_customer_id_foreign` (`customer_id`)
) ENGINE=MyISAM AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `works`
--

INSERT INTO `works` (`id`, `tipo_lavoro`, `customer_id`, `status_lavoro`, `data_esecuzione`, `costo_lavoro`, `modalita_pagamento`, `nome_partenza`, `indirizzo_partenza`, `latitude_partenza`, `longitude_partenza`, `materiale`, `codice_eer`, `nome_destinazione`, `indirizzo_destinazione`, `latitude_destinazione`, `longitude_destinazione`, `created_at`, `updated_at`) VALUES
(5, 'Smaltimento', 2, 'Concluso', NULL, NULL, NULL, 'cliente', 'Via Salaria, Roma, RM, Italia', 41.9873505, 12.5114952, 'ferro', '117/54', 'deposito', 'Via Napoli, Pozzuoli, NA, Italia', 40.8187237, 14.1460694, '2025-03-18 15:23:16', '2025-05-17 11:42:56'),
(6, 'Trasporto', 1, 'In Sospeso', '2025-04-02', 100.00, 'Contanti', 'cliente', 'Via Appia Antica, Roma, RM, Italia', 41.8183952, 12.5584069, 'legno', '137/07', 'cantiere', 'Via del Corso, Roma, RM, Italia', 41.9032103, 12.4795059, '2025-03-28 08:36:17', '2025-03-28 08:36:17'),
(7, 'Trasporto', 1, 'In Sospeso', '2025-04-10', 100.00, 'Contanti', 'cliente', 'Via Appia Antica, Roma, RM, Italia', 41.8183952, 12.5584069, 'calcinaccio', '11708', 'cantiere', 'Via del Corso, Roma, RM, Italia', 41.9032103, 12.4795059, '2025-03-28 14:42:05', '2025-03-28 14:42:05'),
(8, 'Smaltimento', 1, 'Concluso', '2025-05-17', 100.00, 'Contanti', 'cliente', 'Via Appia Antica, Roma, RM, Italia', 41.8183952, 12.5584069, 'legno', '137/07', 'deposito', 'Via Cappuccini, Caserta, CE, Italia', 41.0862173, 14.3410947, '2025-05-17 10:45:15', '2025-05-17 11:15:12'),
(9, 'Trasporto', 3, 'Concluso', '2025-05-17', 200.00, 'Contanti', 'cliente', 'Via Giotto, 13, Caserta, CE, Italia', 41.0798348, 14.3343470, 'legno', '137/07', 'cantiere', 'Via del Corso, Roma, RM, Italia', 41.9032103, 12.4795059, '2025-05-17 12:01:18', '2025-05-17 12:08:22'),
(10, 'Trasporto', 1, 'In Sospeso', '2025-05-28', 100.00, 'Contanti', 'cantiere', 'Via del Corso, Roma, RM, Italia', 41.9032103, 12.4795059, 'legno', '137/07', 'cliente', 'Via Appia Antica, Roma, RM, Italia', 41.8183952, 12.5584069, '2025-05-19 10:34:30', '2025-05-19 10:34:30'),
(11, 'Trasporto', 3, 'Concluso', '2025-05-19', 200.00, 'Contanti', 'cliente', 'Via Giotto, 13, Caserta, CE, Italia', 41.0798348, 14.3343470, 'legno', '137/07', 'cantiere', 'Via del Corso, Roma, RM, Italia', 41.9032103, 12.4795059, '2025-05-19 13:30:46', '2025-05-19 13:33:01'),
(12, 'Trasporto', 1, 'Concluso', '2025-05-21', 200.00, 'Bonifico', 'cliente', 'Via Appia Antica, Roma, RM, Italia', 41.8183952, 12.5584069, 'Frigorifero', NULL, 'libero', 'Via Conca d\'Oro, Roma, RM, Italia', 41.9405202, 12.5243597, '2025-05-21 11:02:04', '2025-05-21 11:04:13'),
(13, 'Trasporto', 1, 'Concluso', '2025-05-21', 100.00, 'Contanti', 'libero', 'Via Conca d\'Oro, Roma, RM, Italia', 41.9405202, 12.5243597, 'cucina', NULL, 'libero', 'Via Cadibona, Roma, RM, Italia', 41.9548933, 12.5377561, '2025-05-21 13:03:17', '2025-05-21 13:11:01'),
(14, 'Smaltimento', 4, 'In Sospeso', '2025-05-21', 50.00, 'Contanti', 'cliente', 'Corso Luigi Cadorna, Campanarello, AV, Italia', 41.0473890, 14.9175705, 'calcinaccio', '11708', 'deposito', 'Via Casal Bianco, 269, Guidonia Montecelio, RM, Italia', 41.9545425, 12.6599241, '2025-05-21 13:26:47', '2025-05-21 13:26:47');

-- --------------------------------------------------------

--
-- Struttura della tabella `work_worker`
--

DROP TABLE IF EXISTS `work_worker`;
CREATE TABLE IF NOT EXISTS `work_worker` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `work_id` bigint UNSIGNED NOT NULL,
  `worker_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `work_worker_work_id_worker_id_unique` (`work_id`,`worker_id`),
  KEY `work_worker_worker_id_foreign` (`worker_id`)
) ENGINE=MyISAM AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dump dei dati per la tabella `work_worker`
--

INSERT INTO `work_worker` (`id`, `work_id`, `worker_id`, `created_at`, `updated_at`) VALUES
(1, 1, 3, '2025-03-18 13:25:36', '2025-03-18 13:25:36'),
(2, 5, 3, '2025-03-18 15:38:23', '2025-03-18 15:38:23'),
(3, 5, 5, '2025-05-17 08:05:24', '2025-05-17 08:05:24'),
(4, 5, 6, '2025-05-17 08:11:16', '2025-05-17 08:11:16'),
(5, 8, 6, '2025-05-17 10:45:26', '2025-05-17 10:45:26'),
(6, 9, 6, '2025-05-17 12:01:38', '2025-05-17 12:01:38'),
(7, 11, 6, '2025-05-19 13:31:49', '2025-05-19 13:31:49'),
(8, 12, 6, '2025-05-21 11:02:37', '2025-05-21 11:02:37'),
(9, 13, 6, '2025-05-21 13:03:31', '2025-05-21 13:03:31'),
(10, 14, 6, '2025-05-21 13:27:20', '2025-05-21 13:27:20');
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
