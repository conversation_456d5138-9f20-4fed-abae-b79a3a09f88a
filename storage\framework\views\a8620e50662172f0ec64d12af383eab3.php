

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Report Fondo Cassa</h1>
    </div>
    
    <?php if(session('error')): ?>
        <div class="alert alert-danger">
            <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Seleziona Parametri Report</h6>
        </div>
        <div class="card-body">
            <form action="<?php echo e(route('reports.cashflow.generate')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                  <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="worker_id" class="form-label">Dipendente*</label>
                        <select class="form-control" id="worker_id" name="worker_id" required>
                            <option value="">Seleziona dipendente...</option>
                            <?php $__currentLoopData = $workers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $worker): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($worker->id); ?>" <?php echo e(old('worker_id') == $worker->id ? 'selected' : ''); ?>>
                                    <?php echo e($worker->name_worker); ?> <?php echo e($worker->cognome_worker); ?> (<?php echo e($worker->worker_email); ?>)
                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['worker_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="data_inizio" class="form-label">Data Inizio*</label>
                        <input type="date" class="form-control" id="data_inizio" name="data_inizio" value="<?php echo e(old('data_inizio', now()->startOfMonth()->toDateString())); ?>" required>
                        <?php $__errorArgs = ['data_inizio'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="data_fine" class="form-label">Data Fine*</label>
                        <input type="date" class="form-control" id="data_fine" name="data_fine" value="<?php echo e(old('data_fine', now()->toDateString())); ?>" required>
                        <?php $__errorArgs = ['data_fine'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> Genera Report
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\wamp64\www\pbm_group_cline\resources\views/admin/reports/cashflow/index.blade.php ENDPATH**/ ?>