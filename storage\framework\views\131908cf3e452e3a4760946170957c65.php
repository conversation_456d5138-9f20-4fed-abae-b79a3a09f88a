

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <h1 class="h3 mb-4 text-gray-800"><PERSON></h1>
        
        <?php if(session('error')): ?>
            <div class="alert alert-danger">
                <?php echo e(session('error')); ?>

            </div>
        <?php endif; ?>
        
        <?php if(session('success')): ?>
            <div class="alert alert-success">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>
        
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Carte Prepagate Assegnate</h6>
            </div>
            <div class="card-body">
                <?php if($creditCards->count() > 0): ?>                    <div class="table-responsive">
                        <table class="table table-bordered dataTable" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Numero Carta</th>
                                    <th>Saldo</th>
                                    <th class="date-column">Data Assegnazione</th>
                                    <th>Azioni</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $creditCards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($card->id); ?></td>
                                        <td><?php echo e(substr($card->numero_carta, 0, 4) . ' **** **** ' . substr($card->numero_carta, -4)); ?></td>
                                        <td>€ <?php echo e(number_format($card->fondo_carta, 2, ',', '.')); ?></td>
                                        <td class="date-column"><?php echo App\Helpers\DateHelper::formatDate($card->data_assegnazione); ?></td>
                                        <td>
                                            <a href="<?php echo e(route('worker.cards.show', $card->id)); ?>" class="btn btn-primary btn-sm">
                                                <i class="bi bi-eye"></i> Dettagli
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        Non hai carte assegnate al momento.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\wamp64\www\pbm_group_cline\resources\views/worker/cards/index.blade.php ENDPATH**/ ?>