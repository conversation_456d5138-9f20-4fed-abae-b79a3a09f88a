/**
 * Date Format Styling
 * This stylesheet provides styling for date fields to ensure consistent presentation
 */

/* Styling for date columns in tables */
.date-column, 
.datetime-column {
    min-width: 110px;
    white-space: nowrap;
}

/* Custom styling for date input fields */
.italian-date-input {
    position: relative;
    display: block;
    width: 100%;
}

/* Date field display format */
.flatpickr-input {
    width: 100%;
    padding-right: 30px; /* Space for icon */
}

/* Date field placeholder */
.flatpickr-input::placeholder {
    color: #aaa;
    font-size: 0.9em;
}

/* Validation styles */
.italian-date-input.is-invalid .flatpickr-input {
    border-color: #dc3545;
}

.italian-date-input.is-valid .flatpickr-input {
    border-color: #28a745;
}

/* Bottone per cancellare data */
.date-clear-button {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 16px;
    line-height: 1;
    color: #6c757d;
    cursor: pointer;
    z-index: 3;
    padding: 0 8px;
}

.date-clear-button:hover {
    color: #dc3545;
}

/* FlatPickr personalizzato */
.flatpickr-calendar {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
    background: #4e73df;
    border-color: #4e73df;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
    box-shadow: -10px 0 0 #4e73df;
}
