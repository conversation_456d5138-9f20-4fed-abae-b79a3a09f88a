

<?php $__env->startSection('content'); ?>
<div class="container-fluid mt-4">
  <div class="card shadow mb-4">
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary">Assegna Lavori</h6>
    </div>
    <div class="card-body">
      <?php if(session('success')): ?>
        <div class="alert alert-success">
          <?php echo e(session('success')); ?>

        </div>
      <?php endif; ?>
      
      <?php if(session('error')): ?>
        <div class="alert alert-danger">
          <?php echo e(session('error')); ?>

        </div>
      <?php endif; ?>

      <!-- Form di assegnazione -->
      <div class="card">
        <div class="card-header">
          <h6 class="m-0 font-weight-bold text-primary">Nuova Assegnazione</h6>
        </div>
        <div class="card-body">
          <form action="<?php echo e(route('work.assignments.store')); ?>" method="POST">
            <?php echo csrf_field(); ?>
            
            <div class="row">
              <div class="col-md-5">
                <div class="mb-3">
                  <label for="work_id" class="form-label">Seleziona Lavoro</label>
                  <select name="work_id" id="work_id" class="form-control" required>
                    <option value="">-- Seleziona un lavoro --</option>
                    <?php $__currentLoopData = $works; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $work): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <?php if($work->status_lavoro === 'In Sospeso'): ?>
                        <option value="<?php echo e($work->id); ?>"><?php echo App\Helpers\DateHelper::formatDateTime($work->created_at); ?> - <?php echo e($work->tipo_lavoro); ?> (<?php echo e($work->status_lavoro); ?>) (<?php echo e($work->customer->full_name ?? $work->customer->ragione_sociale ?? 'N/D'); ?>)</option>
                      <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </select>
                </div>
              </div>
              
              <div class="col-md-5">
                <div class="mb-3">
                  <label for="worker_id" class="form-label">Seleziona Lavoratore</label>
                  <select name="worker_id" id="worker_id" class="form-control" required>
                    <option value="">-- Seleziona un lavoratore --</option>
                    <?php $__currentLoopData = $workers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $worker): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <option value="<?php echo e($worker->id); ?>"><?php echo e($worker->full_name); ?> (<?php echo e($worker->id_worker); ?>)</option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </select>
                </div>
              </div>
              
              <div class="col-md-2 d-flex align-items-end">
                <div class="mb-3">
                  <button type="submit" class="btn btn-primary">Assegna</button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\wamp64\www\pbm_group_cline\resources\views/works/assignments/create.blade.php ENDPATH**/ ?>