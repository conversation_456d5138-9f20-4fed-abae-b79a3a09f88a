/* public/css/style.css */
/* Gestionale PBM Group - Stili personalizzati */

/* Import Google Fonts - Roboto */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,0,1;wght@300;400;500;700&display=swap');

/* ===== VARIABILI COLORI ===== */
:root {
  /* Colori principali */
  --arancio-mezzo: #D17825;
  --marrone-scuro: #3B2A1C;
  --grigio-carbone: #1E1E1E;
  --grigio-cemento: #4C4C4C;

  /* Colori di supporto */
  --grigio-roccia: #9CA3AF;
  --arancio-chiaro: #F4A261;
  --bianco-sporcato: #F9FAFB;
  --blu-grafite: #1F2937;

  /* Font */
  --font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* ===== STILI GLOBALI ===== */
body {
  background-color: var(--bianco-sporcato);
  font-family: var(--font-family);
  color: var(--grigio-carbone);
  font-weight: 400;
  line-height: 1.6;
}

/* ===== SIDEBAR PERSONALIZZATA ===== */
.sidebar {
  background: linear-gradient(180deg, var(--blu-grafite) 0%, var(--marrone-scuro) 100%) !important;
}

.sidebar .sidebar-brand {
  background-color: var(--marrone-scuro);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar .sidebar-brand-text {
  color: #ffffff !important;
  font-weight: 500;
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 400;
  transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
  color: var(--arancio-chiaro) !important;
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
  color: var(--arancio-mezzo) !important;
  background-color: rgba(209, 120, 37, 0.2);
}

.sidebar .nav-link i {
  color: rgba(255, 255, 255, 0.7);
  margin-right: 0.5rem;
}

.sidebar .nav-link:hover i,
.sidebar .nav-link.active i {
  color: var(--arancio-chiaro);
}

/* Logo nella sidebar */
.sidebar-brand-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 8px;
  margin-right: 10px;
}

/* ===== PULSANTI ===== */
.btn-primary {
  background-color: var(--arancio-mezzo);
  border-color: var(--arancio-mezzo);
  color: #ffffff;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: var(--arancio-chiaro);
  border-color: var(--arancio-chiaro);
  box-shadow: 0 4px 8px rgba(209, 120, 37, 0.3);
  /* Rimozione dell'effetto di traslazione */
}

.btn-secondary {
  background-color: var(--blu-grafite);
  border-color: var(--blu-grafite);
  color: #ffffff;
  font-weight: 500;
}

.btn-secondary:hover {
  background-color: var(--grigio-cemento);
  border-color: var(--grigio-cemento);
}

/* ===== CARDS ===== */
.card {
  border-radius: 12px;
  border: 1px solid var(--grigio-roccia);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  /* Rimozione dell'effetto di traslazione */
}

.card-header {
  background-color: var(--bianco-sporcato);
  border-bottom: 1px solid var(--grigio-roccia);
  color: var(--marrone-scuro);
  font-weight: 500;
}

/* ===== FORM ELEMENTS ===== */
.form-control {
  border: 1px solid var(--grigio-roccia);
  border-radius: 8px;
  color: var(--grigio-carbone);
  font-family: var(--font-family);
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--arancio-mezzo);
  box-shadow: 0 0 0 0.2rem rgba(209, 120, 37, 0.25);
}

.form-label {
  color: var(--grigio-cemento);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* ===== TABLES ===== */
.table {
  color: var(--grigio-carbone);
  font-family: var(--font-family);
}

.table th {
  background-color: var(--bianco-sporcato);
  color: var(--marrone-scuro);
  font-weight: 500;
  border-bottom: 2px solid var(--grigio-roccia);
}

.table td {
  border-bottom: 1px solid var(--grigio-roccia);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(249, 250, 251, 0.5);
}

/* ===== ALERTS ===== */
.alert-success {
  background-color: rgba(209, 120, 37, 0.1);
  border-color: var(--arancio-chiaro);
  color: var(--marrone-scuro);
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  border-color: #dc3545;
  color: #721c24;
}

/* ===== TOPBAR ===== */
.topbar {
  background-color: #ffffff;
  border-bottom: 1px solid var(--grigio-roccia);
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--marrone-scuro);
  color: rgba(255, 255, 255, 0.8);
}

/* ===== COLLAPSE MENU ===== */
.collapse-inner {
  background-color: #ffffff;
  border: 1px solid var(--grigio-roccia);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.collapse-item {
  color: var(--grigio-cemento);
  padding: 0.75rem 1rem;
  font-weight: 400;
  transition: all 0.3s ease;
}

.collapse-item:hover {
  color: var(--arancio-mezzo);
  background-color: rgba(209, 120, 37, 0.1);
  text-decoration: none;
}

.collapse-header {
  color: var(--grigio-cemento);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.5rem 1rem 0.25rem;
}

/* ===== BREADCRUMB ===== */
.breadcrumb {
  background-color: transparent;
  padding: 0;
  margin-bottom: 1rem;
}

.breadcrumb-item {
  color: var(--grigio-cemento);
}

.breadcrumb-item.active {
  color: var(--marrone-scuro);
  font-weight: 500;
}

.breadcrumb-item + .breadcrumb-item::before {
  color: var(--grigio-roccia);
}

/* ===== BADGES ===== */
.badge {
  font-weight: 500;
  border-radius: 6px;
}

.badge-primary {
  background-color: var(--arancio-mezzo);
}

.badge-secondary {
  background-color: var(--grigio-cemento);
}

.badge-success {
  background-color: #28a745;
}

.badge-warning {
  background-color: var(--arancio-chiaro);
  color: var(--marrone-scuro);
}

/* ===== PAGINATION ===== */
.pagination .page-link {
  color: var(--grigio-cemento);
  border-color: var(--grigio-roccia);
  transition: all 0.3s ease;
}

.pagination .page-link:hover {
  color: var(--arancio-mezzo);
  background-color: rgba(209, 120, 37, 0.1);
  border-color: var(--arancio-chiaro);
}

.pagination .page-item.active .page-link {
  background-color: var(--arancio-mezzo);
  border-color: var(--arancio-mezzo);
}

/* ===== DROPDOWN ===== */
.dropdown-menu {
  border: 1px solid var(--grigio-roccia);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
  color: var(--grigio-cemento);
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  color: var(--arancio-mezzo);
  background-color: rgba(209, 120, 37, 0.1);
}

/* ===== MODAL ===== */
.modal-content {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
  background-color: var(--bianco-sporcato);
  border-bottom: 1px solid var(--grigio-roccia);
  border-radius: 12px 12px 0 0;
}

.modal-title {
  color: var(--marrone-scuro);
  font-weight: 600;
}

.modal-footer {
  border-top: 1px solid var(--grigio-roccia);
  background-color: var(--bianco-sporcato);
  border-radius: 0 0 12px 12px;
}

/* ===== PROGRESS BAR ===== */
.progress {
  background-color: var(--grigio-roccia);
  border-radius: 8px;
}

.progress-bar {
  background-color: var(--arancio-mezzo);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .sidebar-brand-logo {
    width: 30px;
    height: 30px;
  }

  .sidebar-brand-text {
    font-size: 0.9rem;
  }

  .card {
    margin-bottom: 1rem;
  }

  .btn {
    font-size: 0.9rem;
  }
}
